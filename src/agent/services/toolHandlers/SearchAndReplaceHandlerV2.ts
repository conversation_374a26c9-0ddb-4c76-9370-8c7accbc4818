import { ToolUse, ToolUseName } from '../../types/message';
import { <PERSON><PERSON><PERSON>el<PERSON>, ToolHandlerContext, ToolHandler } from '../ToolHelpers';
import { EditFileRequest, ReplaceInFileRequest, SearchReplace, TextBlockParamVersion1 } from '../../types/type';
import { EditFileResponse, SayTool } from '../../types/type';
import { getReadablePath } from '@/util/path';
import { fileExistsAtPath } from '@/util/fs';
import { ASSISTANT_NAMESPACE } from '@/util/const';
import path from 'path';
import { LangfuseGenerationClient } from 'langfuse';
import { getFileLanguage } from '@/util/fileType';
import fs from 'fs/promises';
import { GitManager } from '@/index-manager/GitManager';
import { compareContents } from '@/agent/utils/diff';
import { getRelativePath } from '@/util/git-path';
import delay from 'delay';


/**
 * 搜索替换工具处理器
 */
export class SearchAndReplaceHandlerV2 implements ToolHandler {
  private gitManager: GitManager;

  constructor(private context: ToolHandlerContext) {
    this.gitManager = new GitManager();
  }

  private parseDiffAndDisplayContent(
    diff: string,
    partial?: boolean
  ): {
    diffArr: SearchReplace[];
    content: string;
  } {
    const result = {
      diffArr: [] as SearchReplace[],
      content: ''
    };

    if (!diff) return result;

    const lines = partial ? diff.split('\n').slice(0, -1) : diff.split('\n');
    if (lines.length === 0) return result;

    let currentSearch = '';
    let currentReplace = '';
    let inSearch = false;
    let inReplace = false;

    for (const line of lines) {
      // 检查是否是 SEARCH 块开始
      if (line.match(/^[-]{3,} SEARCH$/)) {
        inSearch = true;
        inReplace = false;
        currentSearch = '';
        currentReplace = '';
        continue;
      }

      // 检查是否是分隔符
      if (line.match(/^[=]{3,}$/)) {
        inSearch = false;
        inReplace = true;
        continue;
      }

      // 检查是否是 REPLACE 块结束
      if (line.match(/^[+]{3,} REPLACE$/)) {
        inSearch = false;
        inReplace = false;

        // 添加到结果数组
        if (currentSearch || currentReplace) {
          result.content += currentReplace;
          result.diffArr.push({
            search: currentSearch.trim(),
            replace: currentReplace.trim()
          });
        }

        currentSearch = '';
        currentReplace = '';
        continue;
      }

      // 累积内容
      if (inSearch) {
        currentSearch += line + '\n';
      } else if (inReplace) {
        currentReplace += line + '\n';
      }
    }

    // 处理最后一个未完成的块
    if (inReplace && (currentSearch || currentReplace)) {
      result.content += currentReplace;
      result.diffArr.push({
        search: currentSearch,
        replace: currentReplace
      });
    }

    return result;
  }

  /**
   * 处理编辑结果的通用方法
   */
  private async handleEditResult(
    data: EditFileResponse | undefined,
    block: ToolUse,
    userMessageContent: TextBlockParamVersion1[],
    validRelPath: string
  ): Promise<void> {
    this.context.messageService.say('edit_file_result', JSON.stringify(data));

    if (data?.noModified) {
      ToolHelpers.pushToolResult(
        block,
        userMessageContent,
        `No changes needed for '${validRelPath}'`,
        this.context.stateManager
      );
      return;
    }

    if (data?.type === 'success') {
      // 将修改的文件路径添加到chatModifiedFilelist中（相对路径），并去重
      // const currentModifiedFiles = this.context.stateManager.getState().chatModifiedFilelist;
      // const relRecorded = validRelPath;
      // if (!currentModifiedFiles.includes(relRecorded)) {
      //   this.context.stateManager.updateState({
      //     chatModifiedFilelist: [...currentModifiedFiles, relRecorded]
      //   });
      // }

      const resultMessage = [
        `The updated content has been successfully saved to ${validRelPath}.\n\n`,
        `Please note:\n`,
        `1. You do not need to re-write the file with these changes, as they have already been applied.\n`,
        `2. Proceed with the task using the updated file content as the new baseline.\n`,
        `3. If the user's edits have addressed part of the task or changed the requirements, adjust your approach accordingly.`
      ].join('');
      ToolHelpers.pushToolResult(block, userMessageContent, resultMessage, this.context.stateManager);
    } else {
      await ToolHelpers.handleError(
        block,
        userMessageContent,
        this.context,
        null,
        'replace in file',
        new Error(data?.content || ''),
        'replace_in_file'
      );
    }

    // 保存检查点
    await this.context.checkpointService?.saveCheckpoint();
  }

  async handle(block: ToolUse, userMessageContent: TextBlockParamVersion1[]): Promise<void> {
    let generationCall: LangfuseGenerationClient | null | undefined = null;

    // Extract and validate parameters
    const relPath: string | undefined = block.params.path;
    const diff: string | undefined = block.params.diff; // for replace_in_file

    try {
      const { diffArr, content } = this.parseDiffAndDisplayContent(diff || '', block.partial);
      const sharedMessageProps: SayTool = {
        tool: 'editFile',
        path: getReadablePath(this.context.cwd, relPath),
        content,
        tool_version: 'v2'
      };

      // Handle partial tool use
      if (block.partial) {
        await this.context.messageService
          .say('tool', JSON.stringify(sharedMessageProps), block.partial)
          .catch(() => {});
        return;
      }

      // Validate required parameters
      if (!relPath) {
        this.context.stateManager.updateState({
          consecutiveMistakeCount: this.context.stateManager.getState().consecutiveMistakeCount + 1
        });
        const errorMessage = await ToolHelpers.sayAndCreateMissingParamError('replace_in_file', 'path', this.context);
        ToolHelpers.pushToolResult(block, userMessageContent, errorMessage, this.context.stateManager);
        return;
      }

      // Validate replacements array if provided
      if (!diff) {
        this.context.stateManager.updateState({
          consecutiveMistakeCount: this.context.stateManager.getState().consecutiveMistakeCount + 1
        });
        const errorMessage = await ToolHelpers.sayAndCreateMissingParamError('replace_in_file', 'diff', this.context);
        ToolHelpers.pushToolResult(block, userMessageContent, errorMessage, this.context.stateManager);
        return;
      }

      // At this point we know relPath is defined
      const validRelPath = relPath as string;
      const absolutePath = path.resolve(this.context.cwd, validRelPath);
      const startToolTime = Date.now();
      const fileExists = await fileExistsAtPath(absolutePath);

      if (!fileExists) {
        this.context.stateManager.updateState({
          consecutiveMistakeCount: this.context.stateManager.getState().consecutiveMistakeCount + 1
        });
        const formattedError = `File does not exist at path: ${absolutePath}\nThe specified file could not be found. Please verify the file path and try again.`;
        await this.context.messageService.say('error', formattedError);
        ToolHelpers.pushToolResult(block, userMessageContent, formattedError, this.context.stateManager);
        return;
      }

      // Reset consecutive mistakes since all validations passed
      this.context.stateManager.updateState({ consecutiveMistakeCount: 0 });
      // Read and process file content
      let fileContent: string;
      try {
        fileContent = await fs.readFile(absolutePath, 'utf-8');
      } catch (error) {
        this.context.stateManager.updateState({
          consecutiveMistakeCount: this.context.stateManager.getState().consecutiveMistakeCount + 1
        });
        const errorMessage = `Error reading file: ${absolutePath}\nFailed to read the file content: ${
          error instanceof Error ? error.message : String(error)
        }\nPlease verify file permissions and try again.`;
        await this.context.messageService.say('error', errorMessage);
        ToolHelpers.pushToolResult(block, userMessageContent, errorMessage, this.context.stateManager);
        return;
      }

      // 计算diffLines之和
      const diffLines = diffArr.reduce((total, item) => {
        try {
          const search = String(item.search || '');
          const replace = String(item.replace || '');
          const diffContent = compareContents(search, replace);
          return total + (diffContent?.addedLines?.length || 0) + (diffContent?.deletedLines?.length || 0);
        } catch (error) {
          return total;
        }
      }, 0);

      this.context.loggerManager.reportUserAction({
        key: 'agent_tools_request',
        type: 'replace_in_file',
        content: JSON.stringify({
          toolName: block.name,
          sessionId: this.context.stateManager.getState().sessionId,
          chatId: this.context.stateManager.getState().chatId,
          params: {
            path: absolutePath,
            fileType: getFileLanguage(absolutePath),
            lines: fileContent.split('\n').length,
            diffLines
          }
        })
      });
      // 上报当前请求参数和headers，block内容
      // this.context.loggerManager.reportUserAction({
      //   key: 'agent_tool_edit_file:dataset',
      //   type: 'replace_in_file',
      //   content: JSON.stringify({
      //     toolName: block.name,
      //     sessionId: this.context.stateManager.getState().sessionId,
      //     chatId: this.context.stateManager.getState().chatId,
      //     path: absolutePath,
      //     fileType: getFileLanguage(absolutePath),
      //     fileContent,
      //     block,
      //     reqInfo: this.context.agentManager?.getCurrentRequestParams(),
      //   })
      // });
      // this.context.loggerManager.getAgentDataSetLogger().info(`请求参数信息：${JSON.stringify({
      //   toolName: block.name,
      //   sessionId: this.context.stateManager.getState().sessionId,
      //   chatId: this.context.stateManager.getState().chatId,
      //   path: absolutePath,
      //   fileType: getFileLanguage(absolutePath),
      //   fileContent,
      //   block,
      //   reqInfo: this.context.agentManager?.getCurrentRequestParams(),
      // })}`);
      const completeMessage = JSON.stringify({
        ...sharedMessageProps,
        diff: diff
      } satisfies SayTool);

      // Parse diff to extract search and replace arrays

      // Report code generation
      if (this.context.agentManager) {
        // 获取文件相对于 git 仓库根目录的路径
        const gitRelativePath = await getRelativePath(absolutePath, this.context.cwd);

        await this.context.agentManager.reportGenerateCode(
          diffArr.map((item) => ({ ...item, filePath: validRelPath, gitRelativePath: gitRelativePath || undefined }))
        );
      }

      await this.context.messageService.say('tool', completeMessage, block.partial);
      await delay(1000); 
      generationCall = this.context.loggerManager.getTrace()?.generation({
        name: 'tool_call',
        input: {
          path: validRelPath,
          diff,
          diffArr
        },
        metadata: {
          name: block.name
        }
      });

      if (!this.context.messenger) {
        throw new Error('Messenger not available for writeToFile');
      }
      const { data } = await this.context.messenger.request('assistant/agent/replaceInFile', {
        path: relPath || '',
        searchReplaceInfo: diffArr,
        diff,
        sessionId: this.context.stateManager.getState().sessionId,
        chatId: this.context.stateManager.getState().chatId
      });

      // 计算replaceLines之和
      const replaceLines = diffArr.reduce((sum, i) => sum + i.replace.length, 0);

      ToolHelpers.reportToolAction(this.context, block, Date.now() - startToolTime, {
        noModified: !!data?.noModified,
        type: data?.type,
        lines: fileContent.split('\n').length,
        path: absolutePath,
        fileType: getFileLanguage(absolutePath),
        replaceLines,
        diffLines
      });

      // 使用统一的处理方法
      await this.handleEditResult(data, block, userMessageContent, validRelPath);

      generationCall?.end({
        output: { type: data?.type, content: data?.content }
      });

      this.context.loggerManager.perf({
        namespace: ASSISTANT_NAMESPACE,
        subtag: 'kwaipilot-ide-agent-chat-tool',
        millis: Date.now() - startToolTime,
        extra4: data?.type === 'success' ? 'success' : 'error',
        extra6: block.name
      });
    } catch (error: any) {
      await ToolHelpers.handleError(
        block,
        userMessageContent,
        this.context,
        generationCall,
        'replace in file',
        error,
        'replace_in_file'
      );
    }
  }
}
